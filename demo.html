<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DocLink - Premium Telemedicine Platform for Ghana & Beyond</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --doclink-primary: #1A1A2E;
            --doclink-navy: #0A0A2A;
            --doclink-gold: #FFD700;
            --doclink-orange: #FFA500;
            --white: #FFFFFF;
            --off-white: #F8F8F8;
            --light-gray: #CCCCCC;
            --dark-gray: #555555;
            --success: #28A745;
            --shadow-sm: 0px 2px 4px rgba(0, 0, 0, 0.1);
            --shadow-md: 0px 4px 8px rgba(0, 0, 0, 0.15);
            --shadow-lg: 0px 8px 16px rgba(0, 0, 0, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', Arial, sans-serif;
            line-height: 1.6;
            color: var(--dark-gray);
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        /* Header */
        .header {
            background: var(--doclink-primary);
            color: var(--white);
            padding: 16px 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .header.scrolled {
            padding: 12px 0;
            background: rgba(26, 26, 46, 0.95);
            backdrop-filter: blur(10px);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 24px;
            font-weight: 700;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--doclink-gold);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--doclink-primary);
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 32px;
            align-items: center;
        }

        .nav-links a {
            color: var(--white);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--doclink-gold);
        }

        .cta-button {
            background: var(--doclink-gold);
            color: var(--doclink-primary);
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .cta-button:hover {
            background: var(--doclink-orange);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .cta-button.secondary {
            background: var(--doclink-navy);
            color: var(--white);
            border: 2px solid var(--doclink-gold);
        }

        .cta-button.secondary:hover {
            background: var(--doclink-gold);
            color: var(--doclink-primary);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--doclink-navy) 0%, var(--doclink-primary) 100%);
            color: var(--white);
            padding: 120px 0 80px;
            position: relative;
            overflow: hidden;
            {{/*  z-index: 999;  */}}
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 64px;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-text h1 {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 24px;
            line-height: 1.2;
        }

        .hero-text .highlight {
            color: var(--doclink-gold);
        }

        .hero-text p {
            font-size: 20px;
            margin-bottom: 32px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .hero-visual {
            position: relative;
        }

        .hero-image-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .hero-main-image {
            max-width: 100%;
            height: auto;
            max-height: 500px;
            border-radius: 16px;
            box-shadow: 0px 20px 40px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }

        .hero-main-image:hover {
            transform: scale(1.05);
        }

        .hero-image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .floating-stat {
            position: absolute;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 16px 20px;
            box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 215, 0, 0.3);
            text-align: center;
            animation: float 3s ease-in-out infinite;
        }

        .floating-stat .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: var(--doclink-primary);
            display: block;
            line-height: 1;
        }

        .floating-stat .stat-label {
            font-size: 12px;
            color: var(--dark-gray);
            opacity: 0.8;
            margin-top: 4px;
        }

        .stat-1 {
            top: 10%;
            left: -10%;
            animation-delay: 0s;
        }

        .stat-2 {
            top: 20%;
            right: -15%;
            animation-delay: 0.5s;
        }

        .stat-3 {
            bottom: 30%;
            left: -5%;
            animation-delay: 1s;
        }

        .stat-4 {
            bottom: 10%;
            right: -10%;
            animation-delay: 1.5s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        /* Legacy styles for backward compatibility */
        .hero-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 32px;
            border: 1px solid rgba(255, 215, 0, 0.2);
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 24px;
            margin-top: 24px;
        }

        .stat {
            text-align: center;
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: var(--doclink-gold);
            display: block;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.8;
        }

        /* Features Section */
        .features {
            padding: 80px 0;
            background: var(--off-white);
        }

        .section-header {
            text-align: center;
            margin-bottom: 64px;
        }

        .section-header h2 {
            font-size: 36px;
            font-weight: 700;
            color: var(--doclink-primary);
            margin-bottom: 16px;
        }

        .section-header p {
            font-size: 18px;
            color: var(--dark-gray);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 32px;
        }

        .feature-card {
            background: var(--white);
            padding: 32px;
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 215, 0, 0.1);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
            border-color: var(--doclink-gold);
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--doclink-gold), var(--doclink-orange));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 24px;
            color: var(--doclink-primary);
            font-size: 24px;
        }

        .feature-card h3 {
            font-size: 24px;
            font-weight: 600;
            color: var(--doclink-primary);
            margin-bottom: 16px;
        }

        .feature-card p {
            color: var(--dark-gray);
            line-height: 1.6;
        }

        /* Technology Section */
        .technology {
            padding: 80px 0;
            background: var(--doclink-primary);
            color: var(--white);
        }

        .tech-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 64px;
            align-items: center;
        }

        .tech-list {
            list-style: none;
        }

        .tech-list li {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .tech-list li:hover {
            background: rgba(255, 215, 0, 0.1);
            transform: translateX(8px);
        }

        .tech-icon {
            width: 40px;
            height: 40px;
            background: var(--doclink-gold);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--doclink-primary);
            flex-shrink: 0;
        }

        /* Kiosk Section */
        .kiosk {
            padding: 80px 0;
            background: var(--white);
        }

        .kiosk-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 64px;
            align-items: center;
        }

        .kiosk-visual {
            background: linear-gradient(135deg, var(--doclink-navy), var(--doclink-primary));
            border-radius: 16px;
            padding: 48px;
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .kiosk-visual::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        .device-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-top: 24px;
        }

        .device-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 16px;
            border-radius: 8px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        /* Pricing Section */
        .pricing {
            padding: 80px 0;
            background: var(--off-white);
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 32px;
            margin-top: 48px;
        }

        .pricing-card {
            background: var(--white);
            border-radius: 16px;
            padding: 40px 32px;
            text-align: center;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            border: 2px solid transparent;
        }

        .pricing-card.featured {
            border-color: var(--doclink-gold);
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: 'Most Popular';
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--doclink-gold);
            color: var(--doclink-primary);
            padding: 8px 24px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }

        .pricing-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-lg);
        }

        .pricing-card.featured:hover {
            transform: scale(1.05) translateY(-8px);
        }

        .plan-name {
            font-size: 24px;
            font-weight: 600;
            color: var(--doclink-primary);
            margin-bottom: 16px;
        }

        .plan-price {
            font-size: 48px;
            font-weight: 700;
            color: var(--doclink-gold);
            margin-bottom: 8px;
        }

        .plan-period {
            color: var(--dark-gray);
            margin-bottom: 32px;
        }

        .plan-features {
            list-style: none;
            margin-bottom: 32px;
        }

        .plan-features li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .plan-features .check {
            color: var(--success);
            font-weight: bold;
        }

        /* CTA Section */
        .final-cta {
            padding: 80px 0;
            background: linear-gradient(135deg, var(--doclink-navy), var(--doclink-primary));
            color: var(--white);
            text-align: center;
        }

        .final-cta h2 {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 24px;
        }

        .final-cta p {
            font-size: 20px;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .cta-buttons {
            display: flex;
            gap: 24px;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Footer */
        .footer {
            background: var(--doclink-primary);
            color: var(--white);
            padding: 48px 0 24px;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 32px;
        }

        .footer-section h3 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--doclink-gold);
        }

        .footer-section p,
        .footer-section a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            line-height: 1.6;
        }

        .footer-section a:hover {
            color: var(--doclink-gold);
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 24px;
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero-content,
            .tech-content,
            .kiosk-content {
                grid-template-columns: 1fr;
                gap: 32px;
            }

            .hero-text h1 {
                font-size: 36px;
            }

            .hero-buttons {
                justify-content: center;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .pricing-card.featured {
                transform: none;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            /* Mobile adjustments for hero image */
            .hero-main-image {
                max-height: 300px;
            }

            .floating-stat {
                padding: 12px 16px;
                font-size: 14px;
            }

            .floating-stat .stat-number {
                font-size: 18px;
            }

            .floating-stat .stat-label {
                font-size: 10px;
            }

            .stat-1, .stat-2, .stat-3, .stat-4 {
                position: static;
                display: inline-block;
                margin: 8px;
                animation: none;
            }

            .hero-image-overlay {
                position: static;
                display: flex;
                justify-content: center;
                flex-wrap: wrap;
                margin-top: 16px;
            }
        }

        /* Animations */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .slide-in-left {
            opacity: 0;
            transform: translateX(-50px);
            transition: all 0.6s ease;
        }

        .slide-in-left.visible {
            opacity: 1;
            transform: translateX(0);
        }

        .slide-in-right {
            opacity: 0;
            transform: translateX(50px);
            transition: all 0.6s ease;
        }

        .slide-in-right.visible {
            opacity: 1;
            transform: translateX(0);
        }

        /* Text color change when over hero section */
        .text-over-hero {
            color: var(--white) !important;
            transition: color 0.3s ease;
        }

        .text-over-hero * {
            color: var(--white) !important;
        }

        

        /* Card styling for section-header when over hero */
        .section-header.card-over-hero {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px) !important;
            border-radius: 16px !important;
            padding: 32px !important;
            box-shadow: 0px 8px 32px rgba(0, 0, 0, 0.2) !important;
            border: 1px solid rgba(255, 215, 0, 0.3) !important;
            transform: translateY(-8px) !important;
            transition: all 0.4s ease !important;
            margin: 16px !important;
        }

        .section-header.card-over-hero h2 {
            color: var(--doclink-primary) !important;
        }

        .section-header.card-over-hero p {
            color: var(--dark-gray) !important;
        }

        /* Prevent features-grid from turning white */
        .features-grid,
        .features-grid *,
        .features-grid .text-over-hero,
        .features-grid .text-over-hero * {
            color: inherit !important;
        }

        .features-grid .feature-card h3 {
            color: var(--doclink-primary) !important;
        }

        .features-grid .feature-card p {
            color: var(--dark-gray) !important;
        }

        /* Prevent floating stats from turning white */
        .floating-stat,
        .floating-stat *,
        .floating-stat .text-over-hero,
        .floating-stat .text-over-hero * {
            color: inherit !important;
        }

        .floating-stat .stat-number {
            color: var(--doclink-primary) !important;
        }

        .floating-stat .stat-label {
            color: var(--dark-gray) !important;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header" id="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-stethoscope"></i>
                    </div>
                    <span>DocLink</span>
                </div>
                <ul class="nav-links">
                    <li><a href="#features">Features</a></li>
                    <li><a href="#technology">Technology</a></li>
                    <li><a href="#pricing">Pricing</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
                <a href="#contact" class="cta-button">Get Started</a>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text fade-in">
                    <div class="hero-text">
                        <h1>Revolutionizing Healthcare Access in <span class="highlight">Ghana & Beyond</span></h1>
                        <p>DocLink is a premium telemedicine platform that connects patients with healthcare professionals through intelligent mobile apps and smart diagnostic kiosks.</p>
                    </div>
                    <div class="hero-buttons">
                        <a href="#demo" class="cta-button">
                            <i class="fas fa-play-circle" style="margin-right: 8px;"></i>
                            Watch Demo
                        </a>
                        <a href="#pricing" class="cta-button secondary">
                            <i class="fas fa-rocket" style="margin-right: 8px;"></i>
                            Start Your Journey
                        </a>
                    </div>
                </div>
                <div class="hero-visual slide-in-right">
                    <div class="hero-image-container">
                        <img src="doc-removebg-preview.png" alt="DocLink Platform Preview" class="hero-main-image">
                        <div class="hero-image-overlay">
                            <div class="floating-stat stat-1">
                                <span class="stat-number">50K+</span>
                                <span class="stat-label">Patients</span>
                            </div>
                            <div class="floating-stat stat-2">
                                <span class="stat-number">500+</span>
                                <span class="stat-label">Doctors</span>
                            </div>
                            <div class="floating-stat stat-3">
                                <span class="stat-number">24/7</span>
                                <span class="stat-label">Available</span>
                            </div>
                            <div class="floating-stat stat-4">
                                <span class="stat-number">99.9%</span>
                                <span class="stat-label">Uptime</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-header fade-in">
                <h2>Comprehensive Healthcare Solutions</h2>
                <p>From teleconsultations to smart diagnostics, DocLink provides end-to-end healthcare services designed for the Ghanaian market and beyond.</p>
            </div>
            <div class="features-grid">
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3>Teleconsultations</h3>
                    <p>High-quality video, audio, and text consultations with general practitioners and specialists. Integrated with WebRTC and Twilio for seamless communication.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Multi-Language Support</h3>
                    <p>Native support for English and Twi, with intuitive interfaces designed for users across different literacy levels and age groups.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <h3>Smart Diagnostic Kiosks</h3>
                    <p>Integration with community health kiosks equipped with blood pressure monitors, glucometers, thermometers, and other diagnostic tools.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-file-medical"></i>
                    </div>
                    <h3>Electronic Health Records</h3>
                    <p>Secure, HIPAA-compliant EHR system that stores patient history, vitals, medications, and referrals with easy access for healthcare providers.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>Mobile Money Integration</h3>
                    <p>Seamless payment processing with MTN Mobile Money, Vodafone Cash, AirtelTigo Money, plus Paystack and Flutterwave integration.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">
                        <i class="fas fa-prescription-bottle-alt"></i>
                    </div>
                    <h3>E-Prescriptions</h3>
                    <p>Digital prescription management with pharmacy integration, medication tracking, and automated refill reminders for better patient compliance.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Section -->
    <section class="technology" id="technology">
        <div class="container">
            <div class="tech-content">
                <div class="slide-in-left">
                    <h2 style="font-size: 36px; font-weight: 700; margin-bottom: 24px;">Built with Cutting-Edge Technology</h2>
                    <p style="font-size: 18px; margin-bottom: 32px; opacity: 0.9;">DocLink leverages the latest healthcare technology standards to ensure security, scalability, and reliability.</p>
                    <ul class="tech-list">
                        <li>
                            <div class="tech-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div>
                                <strong>End-to-End Encryption</strong><br>
                                <span style="opacity: 0.8;">Military-grade security for all patient data and communications</span>
                            </div>
                        </li>
                        <li>
                            <div class="tech-icon">
                                <i class="fas fa-cloud"></i>
                            </div>
                            <div>
                                <strong>Cloud-Native Architecture</strong><br>
                                <span style="opacity: 0.8;">Scalable infrastructure with 99.9% uptime guarantee</span>
                            </div>
                        </li>
                        <li>
                            <div class="tech-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div>
                                <strong>AI-Powered Triage</strong><br>
                                <span style="opacity: 0.8;">Intelligent symptom assessment and doctor matching</span>
                            </div>
                        </li>
                        <li>
                            <div class="tech-icon">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <div>
                                <strong>Offline Sync Capability</strong><br>
                                <span style="opacity: 0.8;">Works in low-connectivity areas with auto-sync</span>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="slide-in-right">
                    <div style="background: rgba(255, 255, 255, 0.1); border-radius: 16px; padding: 32px; backdrop-filter: blur(10px);">
                        <h3 style="color: var(--doclink-gold); margin-bottom: 24px; font-size: 24px;">Technical Specifications</h3>
                        <div style="display: grid; gap: 16px;">
                            <div style="display: flex; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                                <span>Platform</span>
                                <span style="color: var(--doclink-gold);">iOS & Android</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                                <span>Video Quality</span>
                                <span style="color: var(--doclink-gold);">Up to 4K HD</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                                <span>Response Time</span>
                                <span style="color: var(--doclink-gold);">&lt; 200ms</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; padding: 12px 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                                <span>Data Encryption</span>
                                <span style="color: var(--doclink-gold);">AES-256</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; padding: 12px 0;">
                                <span>Compliance</span>
                                <span style="color: var(--doclink-gold);">HIPAA, GDPR</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Kiosk Integration Section -->
    <section class="kiosk">
        <div class="container">
            <div class="kiosk-content">
                <div class="slide-in-left">
                    <h2 style="font-size: 36px; font-weight: 700; color: var(--doclink-primary); margin-bottom: 24px;">Smart Health Kiosks</h2>
                    <p style="font-size: 18px; margin-bottom: 32px;">Extend healthcare reach to remote communities with our integrated smart diagnostic booths equipped with professional medical devices.</p>
                    <div style="display: grid; gap: 16px;">
                        <div style="display: flex; align-items: center; gap: 16px; padding: 16px; background: var(--off-white); border-radius: 8px;">
                            <div style="width: 40px; height: 40px; background: var(--doclink-gold); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--doclink-primary);">
                                <i class="fas fa-heartbeat"></i>
                            </div>
                            <div>
                                <strong style="color: var(--doclink-primary);">Vital Signs Monitoring</strong><br>
                                <span style="color: var(--dark-gray);">Blood pressure, heart rate, temperature, and weight measurements</span>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 16px; padding: 16px; background: var(--off-white); border-radius: 8px;">
                            <div style="width: 40px; height: 40px; background: var(--doclink-gold); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--doclink-primary);">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div>
                                <strong style="color: var(--doclink-primary);">Blood Glucose Testing</strong><br>
                                <span style="color: var(--dark-gray);">Instant diabetes screening and monitoring capabilities</span>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 16px; padding: 16px; background: var(--off-white); border-radius: 8px;">
                            <div style="width: 40px; height: 40px; background: var(--doclink-gold); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--doclink-primary);">
                                <i class="fas fa-wifi"></i>
                            </div>
                            <div>
                                <strong style="color: var(--doclink-primary);">Real-time Data Sync</strong><br>
                                <span style="color: var(--dark-gray);">Instant transmission to healthcare providers for immediate consultation</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="kiosk-visual slide-in-right">
                    <h3 style="font-size: 24px; margin-bottom: 24px; color: var(--doclink-gold);">Integrated Medical Devices</h3>
                    <div class="device-grid">
                        <div class="device-item">
                            <i class="fas fa-thermometer-half" style="font-size: 24px; margin-bottom: 8px; color: var(--doclink-gold);"></i>
                            <div>Digital Thermometer</div>
                        </div>
                        <div class="device-item">
                            <i class="fas fa-heartbeat" style="font-size: 24px; margin-bottom: 8px; color: var(--doclink-gold);"></i>
                            <div>BP Monitor</div>
                        </div>
                        <div class="device-item">
                            <i class="fas fa-weight" style="font-size: 24px; margin-bottom: 8px; color: var(--doclink-gold);"></i>
                            <div>Digital Scale</div>
                        </div>
                        <div class="device-item">
                            <i class="fas fa-ruler-vertical" style="font-size: 24px; margin-bottom: 8px; color: var(--doclink-gold);"></i>
                            <div>Stadiometer</div>
                        </div>
                        <div class="device-item">
                            <i class="fas fa-tint" style="font-size: 24px; margin-bottom: 8px; color: var(--doclink-gold);"></i>
                            <div>Glucometer</div>
                        </div>
                        <div class="device-item">
                            <i class="fas fa-stethoscope" style="font-size: 24px; margin-bottom: 8px; color: var(--doclink-gold);"></i>
                            <div>Digital Stethoscope</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="pricing" id="pricing">
        <div class="container">
            <div class="section-header fade-in">
                <h2>Choose Your Healthcare Solution</h2>
                <p>Flexible pricing plans designed for healthcare providers, institutions, and government partnerships.</p>
            </div>
            <div class="pricing-grid">
                <div class="pricing-card fade-in">
                    <div class="plan-name">Starter</div>
                    <div class="plan-price">$2,500</div>
                    <div class="plan-period">per month</div>
                    <ul class="plan-features">
                        <li><span class="check">✓</span> Up to 1,000 consultations/month</li>
                        <li><span class="check">✓</span> Basic EHR system</li>
                        <li><span class="check">✓</span> Mobile money integration</li>
                        <li><span class="check">✓</span> 24/7 technical support</li>
                        <li><span class="check">✓</span> Multi-language support</li>
                    </ul>
                    <a href="#contact" class="cta-button" style="width: 100%;">Get Started</a>
                </div>
                <div class="pricing-card featured fade-in">
                    <div class="plan-name">Professional</div>
                    <div class="plan-price">$5,000</div>
                    <div class="plan-period">per month</div>
                    <ul class="plan-features">
                        <li><span class="check">✓</span> Up to 5,000 consultations/month</li>
                        <li><span class="check">✓</span> Advanced EHR with analytics</li>
                        <li><span class="check">✓</span> Smart kiosk integration (5 units)</li>
                        <li><span class="check">✓</span> AI-powered triage</li>
                        <li><span class="check">✓</span> Custom branding</li>
                        <li><span class="check">✓</span> Priority support</li>
                    </ul>
                    <a href="#contact" class="cta-button" style="width: 100%;">Most Popular</a>
                </div>
                <div class="pricing-card fade-in">
                    <div class="plan-name">Enterprise</div>
                    <div class="plan-price">Custom</div>
                    <div class="plan-period">tailored pricing</div>
                    <ul class="plan-features">
                        <li><span class="check">✓</span> Unlimited consultations</li>
                        <li><span class="check">✓</span> Full platform customization</li>
                        <li><span class="check">✓</span> Unlimited kiosk integration</li>
                        <li><span class="check">✓</span> Dedicated account manager</li>
                        <li><span class="check">✓</span> Government compliance</li>
                        <li><span class="check">✓</span> On-premise deployment option</li>
                    </ul>
                    <a href="#contact" class="cta-button secondary" style="width: 100%;">Contact Sales</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta">
        <div class="container fade-in">
            <h2>Ready to Transform Healthcare in Your Community?</h2>
            <p>Join leading healthcare providers who trust DocLink to deliver exceptional patient care through innovative telemedicine solutions.</p>
            <div class="cta-buttons">
                <a href="#demo" class="cta-button">
                    <i class="fas fa-calendar-alt" style="margin-right: 8px;"></i>
                    Schedule Demo
                </a>
                <a href="#contact" class="cta-button secondary">
                    <i class="fas fa-phone" style="margin-right: 8px;"></i>
                    Contact Sales Team
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" id="contact">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                        <div style="width: 40px; height: 40px; background: var(--doclink-gold); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: var(--doclink-primary);">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                        <h3 style="color: var(--white); margin: 0;">DocLink</h3>
                    </div>
                    <p>Revolutionizing healthcare access across Ghana and beyond through innovative telemedicine solutions.</p>
                    <p style="margin-top: 16px;"><strong>Meditel Incorporated</strong><br>Dr. George Acquah, CEO</p>
                </div>
                <div class="footer-section">
                    <h3>Contact Information</h3>
                    <p><i class="fas fa-envelope" style="margin-right: 8px; color: var(--doclink-gold);"></i> <EMAIL></p>
                    <p><i class="fas fa-phone" style="margin-right: 8px; color: var(--doclink-gold);"></i> +233 (0) 24 123 4567</p>
                    <p><i class="fas fa-map-marker-alt" style="margin-right: 8px; color: var(--doclink-gold);"></i> Accra, Ghana</p>
                </div>
                <div class="footer-section">
                    <h3>Solutions</h3>
                    <p><a href="#features">Teleconsultations</a></p>
                    <p><a href="#features">Smart Kiosks</a></p>
                    <p><a href="#features">EHR Systems</a></p>
                    <p><a href="#features">Mobile Integration</a></p>
                </div>
                <div class="footer-section">
                    <h3>Support</h3>
                    <p><a href="#pricing">Pricing</a></p>
                    <p><a href="#">Documentation</a></p>
                    <p><a href="#">Training</a></p>
                    <p><a href="#">24/7 Support</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 DocLink by Meditel Incorporated. All rights reserved. | Transforming Healthcare Access in Ghana & Beyond</p>
            </div>
        </div>
    </footer>

    <script>
        // Header scroll effect
        window.addEventListener('scroll', function() {
            const header = document.getElementById('header');
            if (window.scrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all elements with animation classes
        document.querySelectorAll('.fade-in, .slide-in-left, .slide-in-right').forEach(el => {
            observer.observe(el);
        });

        // Counter animation for hero stats
        function animateCounter(element, originalText, targetNumber, duration = 2000) {
            let start = 0;
            const increment = targetNumber / (duration / 16);

            function updateCounter() {
                start += increment;
                if (start < targetNumber) {
                    const currentNumber = Math.floor(start);
                    // Replace the number part while keeping the original formatting
                    const newText = originalText.replace(/\d+/g, currentNumber.toString());
                    element.textContent = newText;
                    requestAnimationFrame(updateCounter);
                } else {
                    // Restore original text at the end
                    element.textContent = originalText;
                }
            }

            updateCounter();
        }

        // Animate counters when hero section is visible
        const heroObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // Handle both old .stat-number and new .floating-stat .stat-number
                    const counters = entry.target.querySelectorAll('.stat-number');
                    counters.forEach(counter => {
                        const originalText = counter.textContent;
                        const number = parseInt(originalText.replace(/[^\d]/g, ''));
                        if (number) {
                            animateCounter(counter, originalText, number);
                        }
                    });
                    heroObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        const heroSection = document.querySelector('.hero');
        if (heroSection) {
            heroObserver.observe(heroSection);
        }

        // Add hover effects to feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Add click tracking for CTA buttons
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', function(e) {
                // Add analytics tracking here
                console.log('CTA clicked:', this.textContent.trim());
                
                // Add visual feedback
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // Mobile menu toggle (if needed)
        function toggleMobileMenu() {
            const navLinks = document.querySelector('.nav-links');
            navLinks.classList.toggle('mobile-open');
        }

        // Add loading animation
        window.addEventListener('load', function() {
            document.body.classList.add('loaded');
        });

        // Form validation and submission (placeholder)
        function handleFormSubmission(formData) {
            // This would integrate with your backend
            console.log('Form submitted:', formData);
            alert('Thank you for your interest! Our team will contact you within 24 hours.');
        }

        // Add parallax effect to hero section
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Function to check if text elements are over hero section and make them white
        function checkTextOverHero() {
            const hero = document.querySelector('.hero');
            if (!hero) return;

            const heroRect = hero.getBoundingClientRect();
            const heroTop = heroRect.top;
            const heroBottom = heroRect.bottom;

            // Handle section headers specially with card styling
            const sectionHeaders = document.querySelectorAll('.section-header');
            sectionHeaders.forEach(header => {
                const headerRect = header.getBoundingClientRect();
                const headerTop = headerRect.top;
                const headerBottom = headerRect.bottom;

                // Check if section header is overlapping with hero section
                const isOverlapping = (headerTop < heroBottom && headerBottom > heroTop);

                if (isOverlapping) {
                    header.classList.add('card-over-hero');
                } else {
                    header.classList.remove('card-over-hero');
                }
            });

            // Select all other text elements that might scroll over the hero, excluding features section
            const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, a, li, div');

            textElements.forEach(element => {
                // Skip section headers (handled above), features section, floating stats, or their children
                if (element.classList.contains('section-header') ||
                    element.closest('.section-header') ||
                    element.classList.contains('features-grid') ||
                    element.closest('.features-grid') ||
                    element.classList.contains('features') ||
                    element.closest('.features') ||
                    element.classList.contains('feature-card') ||
                    element.closest('.feature-card') ||
                    element.classList.contains('floating-stat') ||
                    element.closest('.floating-stat') ||
                    element.classList.contains('hero-image-overlay') ||
                    element.closest('.hero-image-overlay')) {
                    // Make sure to remove the class if it was previously added
                    element.classList.remove('text-over-hero');
                    return;
                }

                const elementRect = element.getBoundingClientRect();
                const elementTop = elementRect.top;
                const elementBottom = elementRect.bottom;

                // Check if element is overlapping with hero section
                const isOverlapping = (elementTop < heroBottom && elementBottom > heroTop);

                if (isOverlapping) {
                    element.classList.add('text-over-hero');
                } else {
                    element.classList.remove('text-over-hero');
                }
            });
        }

        // Run the check on scroll
        window.addEventListener('scroll', checkTextOverHero);

        // Run the check on page load
        window.addEventListener('load', checkTextOverHero);

        // Run the check on resize
        window.addEventListener('resize', checkTextOverHero);
    </script>
</body>
</html>